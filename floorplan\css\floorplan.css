/* Floorplan CSS */

/* Table styles */
.svg-box {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.svg-box:hover rect {
    stroke-width: 3px;
    stroke: #ffffff;
}

/* Booked table styles */
.booked-disabled {
    cursor: not-allowed !important;
    opacity: 0.95;
    pointer-events: none !important;
}

.booked-disabled rect {
    fill: #cccccc !important;
}

/* Additional selector to ensure booked tables can't be clicked */
[data-booked="true"] {
    pointer-events: none !important;
}

/* Booked label styles */
.booked-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 3px;
    white-space: nowrap;
    z-index: 10;
    pointer-events: none;
}

/* Floor buyout indicator */
.floor-buyout-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(220, 53, 69, 0.2);
    z-index: 5;
    pointer-events: none;
    display: flex;
    justify-content: center;
    align-items: center;
}

.floor-buyout-text {
    background-color: rgba(220, 53, 69, 0.9);
    color: white;
    font-size: 24px;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 5px;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Disabled table styles */
.table-disabled {
    cursor: not-allowed !important;
    opacity: 0.6;
}

.table-disabled rect {
    fill: #999999 !important;
}

/* Selected table styles */
.svg-box[data-value="1"] rect {
    fill: #00FF00 !important;
    /* Green */
}

/* Floor tab styles */
.nav-tabs .nav-link {
    position: relative;
}

.nav-tabs .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
}

.nav-tabs .nav-link .progress {
    height: 4px;
    width: 50px;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Color coding for different rows */
/* Floor 1 */
.floor1-row-a rect {
    fill: #539bff;
    /* Blue */
}

/* Floor 2 */
.floor2-row-b rect {
    fill: #ffaa00;
    /* Yellow */
}

.floor2-row-c rect {
    fill: #ff7700;
    /* Orange */
}

.floor2-row-d rect {
    fill: #ff5584;
    /* Pink */
}

/* Floor 3 */
.floor3-row-e rect {
    fill: #12deb9;
    /* Teal */
}

.floor3-row-f rect {
    fill: #00b33c;
    /* Green */
}

.floor3-row-g rect {
    fill: #73e600;
    /* Light Green */
}

.floor3-row-p rect {
    fill: #ff5584;
    /* Pink */
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    font-family: var(--bs-font-sans-serif);
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: 0.875rem;
    word-wrap: break-word;
    opacity: 0;
}

.tooltip.show {
    opacity: 0.9;
}

.tooltip .tooltip-inner {
    max-width: 200px;
    padding: 0.25rem 0.5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 0.25rem;
}

/* Datepicker styles */
.datepicker {
    z-index: 1060 !important;
    /* Ensure datepicker appears above other elements */
}

.datepicker-dropdown {
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
    background-color: #635bff !important;
    background-image: none !important;
}

.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
    background-color: #ffdb99 !important;
    background-image: none !important;
}

.input-group.date .input-group-text {
    cursor: pointer;
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.input-group.date .form-control {
    cursor: pointer;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.85);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
    /* Add blur effect for modern browsers */
}

.spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 25px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.05);
    min-width: 200px;
    text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .nav-tabs .nav-link .progress {
        width: 30px;
    }

    .nav-tabs .nav-link .badge {
        font-size: 0.6rem;
        padding: 0.1rem 0.3rem;
    }

    .datepicker-dropdown {
        width: 280px;
    }
}