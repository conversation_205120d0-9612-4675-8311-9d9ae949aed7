
POST data: Array
(
    [customer] => MrK<PERSON>
    [phone] => 0631916558
    [voucher] => V
    [agent] => Agent
    [amount] => 1000
    [remark] => Floor 1
    [useDate] => 2025-04-20
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-04-20
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0631916558
    [voucher] => 
    [agent] => Van7
    [amount] => 1000
    [remark] => ABC
    [useDate] => 2025-04-20
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-04-20
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0631916558
    [voucher] => 
    [agent] => Van7
    [amount] => 10000
    [remark] => ABC
    [useDate] => 2025-04-20
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-04-20
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0631916558
    [voucher] => 
    [agent] => Van7
    [amount] => 1000
    [remark] => ABC
    [useDate] => 2025-04-21
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-04-21
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => ABC Company
    [phone] => *********
    [voucher] => ABC
    [agent] => None
    [amount] => 10000
    [remark] => Entire Floor Booking
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => ABC Company
    [phone] => *********
    [voucher] => ABC
    [agent] => None
    [amount] => 10000
    [remark] => Entire Floor Booking
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => ABC Co
    [phone] => *********
    [voucher] => ABC
    [agent] => Chill
    [amount] => 10000
    [remark] => Entire Floor Booking
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => ABC Co
    [phone] => *********
    [voucher] => ABC
    [agent] => Chill
    [amount] => 10000
    [remark] => Entire Floor Booking
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => Van7
    [amount] => 1000
    [remark] => Entire Floor Booking
    [useDate] => 05-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 05-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => Van7
    [amount] => 1000
    [remark] => Entire Floor Booking
    [useDate] => 05-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 05-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 
    [amount] => 1000
    [remark] => Entire Floor Booking
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 
    [amount] => 1000
    [remark] => Entire Floor Booking
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 
    [amount] => 10000
    [remark] => Entire Floor Booking
    [specialRequest] => 2
    [useDate] => 04-07-2025
    [useZone] => 2
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 2
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 
    [amount] => 10000
    [remark] => Entire Floor Booking
    [specialRequest] => 2
    [useDate] => 04-07-2025
    [useZone] => 2
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 2
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => *********
    [voucher] => ABC
    [agent] => 7Day
    [amount] => 10000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [useDate] => 04-07-2025
    [useZone] => 2
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 2
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => BCD
    [agent] => Chill
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 2
    [useDate] => 04-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 04-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0631916558
    [voucher] => CDE
    [agent] => Van7
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 0
    [useDate] => 05-07-2025
    [useZone] => 3
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 05-07-2025
 - useZone: 3
-------------------

POST data: Array
(
    [customer] => ABC
    [phone] => 029009999
    [voucher] => 
    [agent] => 
    [amount] => 10000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [useDate] => 05-07-2025
    [useZone] => 2
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 05-07-2025
 - useZone: 2
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 7Day
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 2
    [paymentType] => Transfer
    [useDate] => 06-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 06-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 
    [agent] => 7Day
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 2
    [paymentType] => Transfer
    [useDate] => 06-07-2025
    [useZone] => 1
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 06-07-2025
 - useZone: 1
-------------------

POST data: Array
(
    [customer] => Anuwat
    [phone] => *********
    [voucher] => ABCD
    [agent] => 7Day
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [paymentType] => Transfer
    [useDate] => 06-07-2025
    [useZone] => 3
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 06-07-2025
 - useZone: 3
-------------------

POST data: Array
(
    [customer] => kopko
    [phone] => 02900900
    [voucher] => 
    [agent] => 7Day
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [paymentType] => Transfer
    [useDate] => 2025-07-07
    [useZone] => 3
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-07-07
 - useZone: 3
-------------------

POST data: Array
(
    [customer] => Anuwat
    [phone] => *********
    [voucher] => ABCDE
    [agent] => 7Day
    [amount] => 1000
    [remark] => Entire Floor Booking
    [specialRequest] => 1
    [paymentType] => Transfer
    [useDate] => 2025-07-07
    [useZone] => 2
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-07-07
 - useZone: 2
-------------------

POST data: Array
(
    [customer] => MrKP
    [phone] => 0865395090
    [voucher] => 1111
    [agent] => 7Day
    [amount] => 10000
    [remark] => Entire Floor Booking
    [specialRequest] => 0
    [paymentType] => Transfer
    [useDate] => 2025-07-07
    [useZone] => 3
)

Required fields check: 
 - customer: present
 - phone: present
 - useDate: 2025-07-07
 - useZone: 3
-------------------
