<div id="main-wrapper">
    <!-- Sidebar Start -->
    <aside class="side-mini-panel with-vertical">
        <!-- ---------------------------------- -->
        <!-- Start Vertical Layout Sidebar -->
        <!-- ---------------------------------- -->
        <div class="iconbar">
            <div>
                <?php
                    include_once("_menuMain.php");
                    include_once("_menuPages.php");
                ?>
            </div>
        </div>
    </aside>
    <!--  Sidebar End -->

    <div class="page-wrapper">
        <?php
            include_once("_menuTop.php");
        ?>

        <div class="body-wrapper">
            <div class="container-fluid">
                <!-- Page Header -->
                <div class="row">
                    <div class="col-12">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <h4 class="card-title text-white mb-0">
                                            <iconify-icon icon="solar:users-group-two-rounded-line-duotone" class="fs-7 me-2"></iconify-icon>
                                            User Management
                                        </h4>
                                        <p class="card-text mb-0">Manage system users and their permissions</p>
                                    </div>
                                    <div>
                                        <span class="badge bg-light text-primary fs-6">
                                            <?php echo ucfirst(str_replace('_', ' ', $_SESSION['role'])); ?>
                                        </span>
                                        <span class="text-white ms-2">Welcome <?php echo ucfirst(str_replace('_', ' ', $_SESSION['role'])); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Add User Section -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="input-group">
                            <span class="input-group-text">
                                <iconify-icon icon="solar:magnifer-line-duotone"></iconify-icon>
                            </span>
                            <input type="text" class="form-control" id="searchUsers" placeholder="Search users by name, username, or email...">
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <iconify-icon icon="solar:user-plus-line-duotone" class="me-2"></iconify-icon>
                            Add New User
                        </button>
                        <div class="btn-group ms-2" role="group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <iconify-icon icon="solar:filter-line-duotone" class="me-1"></iconify-icon>
                                Filter by Role
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item filter-role" href="#" data-role="all">All Roles</a></li>
                                <li><a class="dropdown-item filter-role" href="#" data-role="super_admin">Super Admin</a></li>
                                <li><a class="dropdown-item filter-role" href="#" data-role="admin">Admin</a></li>
                                <li><a class="dropdown-item filter-role" href="#" data-role="user">User</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- System Users Table -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <iconify-icon icon="solar:users-group-rounded-line-duotone" class="me-2"></iconify-icon>
                                    System Users
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover" id="usersTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Username</th>
                                                <th>Email</th>
                                                <th>Role</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                            <!-- Users will be loaded here via JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                                <div id="loadingSpinner" class="text-center py-4" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                                <div id="noUsersMessage" class="text-center py-4" style="display: none;">
                                    <iconify-icon icon="solar:users-group-rounded-line-duotone" class="fs-1 text-muted"></iconify-icon>
                                    <p class="text-muted mt-2">No users found</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Role Permissions Section -->
                <!-- <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <iconify-icon icon="solar:shield-check-line-duotone" class="me-2"></iconify-icon>
                                    Role Permissions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card border-danger">
                                            <div class="card-header bg-danger text-white">
                                                <h6 class="mb-0">
                                                    <iconify-icon icon="solar:crown-line-duotone" class="me-2"></iconify-icon>
                                                    Super Administrator
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>Full system access</li>
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>Manage all users</li>
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>System configuration</li>
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>View all bookings</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card border-warning">
                                            <div class="card-header bg-warning text-dark">
                                                <h6 class="mb-0">
                                                    <iconify-icon icon="solar:settings-line-duotone" class="me-2"></iconify-icon>
                                                    Administrator
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>View all bookings</li>
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>Manage bookings</li>
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>View reports</li>
                                                    <li><iconify-icon icon="solar:close-circle-line-duotone" class="text-danger me-2"></iconify-icon>Limited user management</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <iconify-icon icon="solar:user-line-duotone" class="me-2"></iconify-icon>
                                                    User
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-unstyled mb-0">
                                                    <li><iconify-icon icon="solar:check-circle-line-duotone" class="text-success me-2"></iconify-icon>View bookings</li>
                                                    <li><iconify-icon icon="solar:close-circle-line-duotone" class="text-danger me-2"></iconify-icon>No booking management</li>
                                                    <li><iconify-icon icon="solar:close-circle-line-duotone" class="text-danger me-2"></iconify-icon>No user management</li>
                                                    <li><iconify-icon icon="solar:close-circle-line-duotone" class="text-danger me-2"></iconify-icon>No system access</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">
                    <iconify-icon icon="solar:user-plus-line-duotone" class="me-2"></iconify-icon>
                    Add New User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="addName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="addName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="addUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="addUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="addEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="addEmail" name="email">
                    </div>
                    <div class="alert alert-info">
                        <i class="ti ti-info-circle me-2"></i>
                        <strong>Default Password:</strong> The user will be assigned the default password <code>pwd123</code> and will be required to change it on first login.
                    </div>
                    <div class="mb-3">
                        <label for="addRole" class="form-label">Role</label>
                        <select class="form-select" id="addRole" name="role" required>
                            <option value="">Select Role</option>
                            <?php if ($_SESSION['role'] === 'super_admin'): ?>
                            <option value="super_admin">Super Administrator</option>
                            <option value="admin">Administrator</option>
                            <?php endif; ?>
                            <option value="user">User</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="addStatus" class="form-label">Status</label>
                        <select class="form-select" id="addStatus" name="status" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <iconify-icon icon="solar:user-plus-line-duotone" class="me-2"></iconify-icon>
                        Add User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">
                    <iconify-icon icon="solar:user-edit-line-duotone" class="me-2"></iconify-icon>
                    Edit User
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editUserForm">
                <input type="hidden" id="editUserId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="editName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="editPassword" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Role</label>
                        <select class="form-select" id="editRole" name="role" required>
                            <?php if ($_SESSION['role'] === 'super_admin'): ?>
                            <option value="super_admin">Super Administrator</option>
                            <option value="admin">Administrator</option>
                            <?php endif; ?>
                            <option value="user">User</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editStatus" class="form-label">Status</label>
                        <select class="form-select" id="editStatus" name="status" required>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <iconify-icon icon="solar:diskette-line-duotone" class="me-2"></iconify-icon>
                        Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>