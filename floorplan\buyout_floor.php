<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

// Log POST data for debugging
try {
    file_put_contents("log_buyout_debug.txt", json_encode($_POST) . PHP_EOL, FILE_APPEND);

    // Log more detailed information
    $debug_info = "\nPOST data: " . print_r($_POST, true);
    $debug_info .= "\nRequired fields check: ";
    $debug_info .= "\n - customer: " . (empty($_POST['customer']) ? 'MISSING' : 'present');
    $debug_info .= "\n - phone: " . (empty($_POST['phone']) ? 'MISSING' : 'present');
    $debug_info .= "\n - useDate: " . (empty($_POST['useDate']) ? 'MISSING' : $_POST['useDate']);
    $debug_info .= "\n - useZone: " . (empty($_POST['useZone']) ? 'MISSING' : $_POST['useZone']);

    file_put_contents("log_buyout_detailed.txt", $debug_info . PHP_EOL . "-------------------" . PHP_EOL, FILE_APPEND);
} catch (Exception $e) {
    error_log("Failed to write to log files: " . $e->getMessage());
}

// Check if user is logged in
if (!isset($_SESSION['userKey'])) {
    // For development/testing only
    $_SESSION['userKey'] = 'TEST-KEY-001';
}

include '../dbconnect/_dbconnect.php';  // Include the database connection file

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        if (empty($_POST['customer']) || empty($_POST['phone']) || empty($_POST['useDate']) || empty($_POST['useZone'])) {
            throw new Exception('Missing required fields');
        }

        // Process form data
        $customername = $_POST['customer'];
        $phone = $_POST['phone'];

        // Process text fields
        $voucher = isset($_POST['voucher']) ? $_POST['voucher'] : '';
        $agent = isset($_POST['agent']) ? $_POST['agent'] : '';
        $remark = isset($_POST['remark']) ? $_POST['remark'] : 'Entire Floor Booking';
        $specialRequest = isset($_POST['specialRequest']) ? (int)$_POST['specialRequest'] : 0;
        $amount = isset($_POST['amount']) ? (int)$_POST['amount'] : 0;

        // Process date and zone
        $useDate = $_POST['useDate'];
        $useZone = $_POST['useZone'];
        $userKey = $_SESSION['userKey'];

        // Log the parameters before calling book_floor
        $debug_info = "Calling book_floor with parameters: " . json_encode([
            'customername' => $customername,
            'phone' => $phone,
            'useDate' => $useDate,
            'useZone' => $useZone,
            'voucher' => $voucher,
            'agent' => $agent,
            'remark' => $remark,
            'specialRequest' => $specialRequest,
            'amount' => $amount,
            'userKey' => $userKey
        ]);
        error_log($debug_info);
        file_put_contents("log_buyout_params.txt", $debug_info . "\n", FILE_APPEND);

        // Use the dedicated book_floor function
        $orderNo = book_floor(
            $customername,
            $phone,
            $useDate,
            $useZone,
            $voucher,
            $agent,
            $remark,
            $amount,
            $userKey,
            $specialRequest
        );

        if (!$orderNo) {
            $error_msg = 'Failed to book the entire floor. Please try again.';
            error_log($error_msg);
            file_put_contents("log_buyout_error.txt", $error_msg . "\n", FILE_APPEND);
            throw new Exception($error_msg);
        }

        // Get all tables for the selected floor for the response
        $allTables = get_all_tables_for_floor($useZone);

        // Return success response
        echo json_encode([
            'status' => 'success',
            'message' => 'Entire floor booked successfully',
            'orderNo' => $orderNo,
            'tables' => $allTables
        ]);

    } catch (Exception $e) {
        // Return error response
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
} else {
    // Return error for invalid request method
    echo json_encode([
        'status' => 'error',
        'message' => 'Invalid request method'
    ]);
}

/**
 * Get all tables for a specific floor
 *
 * @param string $floor Floor number
 * @return array Array of table IDs
 */
function get_all_tables_for_floor($floor) {
    $tables = [];
    $prefix = '';

    // Determine the prefix based on floor number
    if ($floor == '1') {
        $prefix = 'A';
    } else if ($floor == '2') {
        $prefix = 'B';
    } else if ($floor == '3') {
        $prefix = 'C';
    } else {
        return $tables; // Return empty array for invalid floor
    }

    // Generate table IDs
    for ($row = 1; $row <= 8; $row++) {
        for ($table = 1; $table <= 15; $table++) {
            $tables[] = "{$prefix}{$row}/{$table}";
        }
    }

    return $tables;
}
?>
