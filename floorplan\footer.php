
  <script src="../assets/js/vendor.min.js"></script>
  <!-- Import Js Files -->
  <script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>
  <script src="../assets/js/theme/app.init.js"></script>
  <script src="../assets/js/theme/theme.js"></script>
  <script src="../assets/js/theme/app.min.js"></script>
  <script src="../assets/js/theme/sidebarmenu.js"></script>

<!-- solar icons -->
<script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>

<!-- SweetAlert2 for better alerts -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- <PERSON>trap Datepicker -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

<!-- Table Naming Script -->
<script src="js/table-naming.js"></script>

<!-- Datepicker Initialization -->
<script src="js/datepicker-init.js?v=1.0.2"></script>

<!-- Agent Scripts -->
<script src="js/load-agents.js"></script>

<script>
$(document).ready(function() {
    console.log('Footer script loaded');

    // Buy Out Floor button click handler
    $('#buyout-floor-btn').on('click', function() {
        console.log('Buy Out Floor button clicked');

        // Get the current floor number
        const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';
        const selectedDate = $('#myDate').val();

        console.log('Current floor:', currentFloor);
        console.log('Selected date:', selectedDate);

        // Set the floor number in the modal
        $('#buyout-useZone').val(currentFloor);
        $('#buyout-useDate').val(selectedDate);

        // Show the modal
        $('#buyoutFloorModal').modal('show');
    });

    // Submit floor buyout
    $('#submit-buyout').on('click', function() {
        // Validate form
        if (!$('#buyoutFloorForm')[0].checkValidity()) {
            $('#buyoutFloorForm')[0].reportValidity();
            return;
        }

        // Get values from the form
        const customer = $('#buyout-customer').val();
        const phone = $('#buyout-phone').val();
        const voucher = $('#buyout-voucher').val() || '';
        const agent = $('#buyout-agent').val() || '';
        const amount = $('#buyout-amount').val() || '0';
        const remark = $('#buyout-remark').val() || 'Entire Floor Booking';
        const specialRequest = $('input[name="specialRequest"]:checked').val() || '0';
        const paymentType = $('input[name="buyoutPaymentType"]:checked').val() || 'Cash';
        const dateValue = $('#buyout-useDate').val();
        const useZone = $('#buyout-useZone').val();

        console.log('Submitting floor buyout:', {
            customer: customer,
            phone: phone,
            date: dateValue,
            floor: useZone
        });

        // Create form data
        const formData = {
            customer: customer,
            phone: phone,
            voucher: voucher,
            agent: agent,
            amount: amount,
            remark: remark,
            specialRequest: specialRequest,
            paymentType: paymentType,
            useDate: dateValue,
            useZone: useZone
        };

        // Show loading indicator
        Swal.fire({
            title: 'Booking entire floor...',
            text: 'Please wait',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Send AJAX request
        $.ajax({
            url: 'buyout_floor.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Floor buyout successful:', response);

                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Entire floor booked successfully!',
                    timer: 2000
                });

                // Close the modal
                $('#buyoutFloorModal').modal('hide');

                // Reset the form
                $('#buyoutFloorForm')[0].reset();

                // Reload the page to show updated data
                setTimeout(() => {
                    location.reload();
                }, 2000);
            },
            error: function(xhr, status, error) {
                console.error('Floor buyout error:', error);
                console.error('Response:', xhr.responseText);

                let errorMessage = 'Failed to book entire floor. Please try again.';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Booking Failed',
                    text: errorMessage
                });
            }
        });
    });
});
</script>
<?php
//2025-03-31
$today = date('Y-m-d');
?>

<!-- get data -->
<script>
$(document).ready(function () {
    const selectedBoxes = new Set();

    // Initialize date picker
    const today = new Date();
    // Format: DD-MM-YYYY
    const formattedDate = today.getDate().toString().padStart(2, '0') + '-' +
                         (today.getMonth() + 1).toString().padStart(2, '0') + '-' +
                         today.getFullYear();
    $('#useDate').val(formattedDate);

    // Format date for display
    function formatDateForDisplay(dateString) {
        try {
            // If already in DD-MM-YYYY format, return as is
            if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                return dateString;
            }

            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateString);
                return dateString; // Return original if invalid
            }
            // Format: DD-MM-YYYY
            return date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            }).replace(/\//g, '-');
        } catch (e) {
            console.error('Error formatting date for display:', e);
            return dateString; // Return original on error
        }
    }

    // Format date for full display
    function formatFullDate(dateString) {
        try {
            // If in DD-MM-YYYY format, convert to a Date object first
            let date;
            if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                const parts = dateString.split('-');
                date = new Date(parts[2], parts[1] - 1, parts[0]);
            } else {
                date = new Date(dateString);
            }

            if (isNaN(date.getTime())) {
                console.error('Invalid date:', dateString);
                return dateString; // Return original if invalid
            }
            // Format: Sunday, March 23, 2025
            return date.toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            });
        } catch (e) {
            console.error('Error formatting full date:', e);
            return dateString; // Return original on error
        }
    };

    // Define formatDateForAPI as a global function if it doesn't exist yet
    if (typeof window.formatDateForAPI !== 'function') {
        // Ensure date is in YYYY-MM-DD format for API calls
        window.formatDateForAPI = function(dateString) {
            try {
                // Handle null or undefined
                if (!dateString) {
                    const today = new Date();
                    return today.toISOString().split('T')[0]; // YYYY-MM-DD
                }

                // Check if already in yyyy-mm-dd format
                if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    return dateString; // Already in correct format
                }

                // Check if the date is in dd-mm-yyyy format
                if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                    // Convert from dd-mm-yyyy to yyyy-mm-dd
                    const parts = dateString.split('-');
                    return `${parts[2]}-${parts[1]}-${parts[0]}`;
                }

                // Check if the date is in dd/mm/yyyy format
                if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                    // Convert from dd/mm/yyyy to yyyy-mm-dd
                    const parts = dateString.split('/');
                    return `${parts[2]}-${parts[1]}-${parts[0]}`;
                }

                // If it's not in a recognized format, try to parse it as a date
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    console.error('Invalid date for API:', dateString);
                    // Return today's date as a fallback
                    const today = new Date();
                    return today.toISOString().split('T')[0]; // YYYY-MM-DD
                }
                return date.toISOString().split('T')[0]; // YYYY-MM-DD
            } catch (e) {
                console.error('Error formatting date for API:', e);
                // Return today's date as a fallback
                const today = new Date();
                return today.toISOString().split('T')[0]; // YYYY-MM-DD
            }
        };
    }

    // Update date display when date input changes
    $('#myDate').on('change', function() {
        const selectedDate = $(this).val();
        $('#shortDate').text(formatDateForDisplay(selectedDate));
        $('#fullDate').text(formatFullDate(selectedDate));
        $('#useDate').val(selectedDate);

        // Reset table selections
        selectedBoxes.clear();
        $('.svg-box').attr('data-value', '0');
        $('rect[id^="rbox-"]').attr('fill', '#539bff');
        $('#div-booking-btn').fadeOut();

        // Load booked tables for the selected date
        window.loadBookedTables(window.formatDateForAPI(selectedDate), $('#useZone').val());
    });

    // Initialize date display
    const initialDate = $('#myDate').val();
    $('#shortDate').text(formatDateForDisplay(initialDate));
    $('#fullDate').text(formatFullDate(initialDate));

    // Make sure the date is properly initialized
    if ($('#shortDate').text() === '') {
        $('#shortDate').text(formatDateForDisplay(initialDate));
        $('#fullDate').text(formatFullDate(initialDate));
    }

    // คลิกเลือกโต๊ะ (Click to select tables)
    $('.svg-box').on('click', function () {
        // Skip if table is already booked
        if ($(this).hasClass('booked-disabled')) {
            return;
        }

        let $box = $(this);
        let id = $box.attr('id'); // เช่น box-A1/1
        let label = id.replace('box-', '');

        // Handle special characters in selector for tables with slashes
        let rectSelector;
        if (label.includes('/')) {
            // Need to escape the slash for jQuery selector
            rectSelector = 'rbox-' + label.replace('/', '\\/');
        } else {
            rectSelector = 'rbox-' + label;
        }

        let $rect = $('#' + rectSelector);
        let data = $box.attr('data-value');

        if (data === '0') {
            $box.attr('data-value', '1');
            $rect.attr('fill', '#00FF00'); // สีเขียว (Green)
            selectedBoxes.add(label);
        } else {
            $box.attr('data-value', '0');
            $rect.attr('fill', '#539bff'); // สีฟ้า (Blue)
            selectedBoxes.delete(label);
        }

        toggleBookingButton();
        updateAdultCount();
    });

    // แสดง/ซ่อนปุ่ม Booking (Show/hide booking button)
    function toggleBookingButton() {
        if (selectedBoxes.size > 0) {
            $('#div-booking-btn').fadeIn();
        } else {
            $('#div-booking-btn').fadeOut();
        }
    }

    // Update adult count based on selected tables (tables × 2)
    function updateAdultCount() {
        // Only update if the booking modal is open
        if ($('#bookingModal').hasClass('show')) {
            const adultCount = selectedBoxes.size * 2;
            $('input[name="adult"]').val(adultCount);
        }
    }

    // คลิกปุ่ม Booking → เปิด Modal (Click booking button to open modal)
    $('#booking-btn').on('click', function () {
        const selected = Array.from(selectedBoxes);
        // Get date from the date picker and use it directly (already in dd-mm-yyyy format)
        const selectedDate = $('#myDate').val();
        // Get current floor number
        const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';

        // Update selected tables display
        $('#selected-tables-display').val(selected.join(','));
        $('#booking-tables').val(selected.join(','));

        // Create badges for selected tables
        const badgesHtml = selected.map(table =>
            `<span class="badge bg-primary me-1 mb-1">${table}</span>`
        ).join('');
        $('#selected-tables-badges').html(badgesHtml);

        // Calculate adults automatically (number of tables × 2)
        const adultCount = selected.length * 2;
        $('input[name="adult"]').val(adultCount);

        $('#useDate').val(selectedDate);
        $('#useZone').val(currentFloor);
        $('#bookingModal').modal('show');
    });

    // กด Add → ส่งข้อมูลไป booking.php (Click Add to send data to booking.php)
    $('#submit-booking').on('click', function () {
        // Validate form
        if (!$('#bookingForm')[0].checkValidity()) {
            $('#bookingForm')[0].reportValidity();
            return;
        }

        // Get values from disabled fields
        const dateValue = $('#useDate').val();
        const apiFormattedDate = window.formatDateForAPI(dateValue);
        const useZone = $('#useZone').val();
        const tables = $('#booking-tables').val();

        console.log('Original date:', dateValue, 'API formatted date:', apiFormattedDate);
        console.log('Floor:', useZone, 'Tables:', tables);

        // Create a new FormData object to include disabled fields
        const formData = $('#bookingForm').serialize() +
                         '&useDate=' + encodeURIComponent(apiFormattedDate) +
                         '&useZone=' + encodeURIComponent(useZone) +
                         '&tables=' + encodeURIComponent(tables);

        // Show loading indicator
        Swal.fire({
            title: 'Saving booking...',
            text: 'Please wait',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: 'booking.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Response from PHP:', response);

                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Booking saved successfully!'
                });

                $('#bookingModal').modal('hide');
                $('#bookingForm')[0].reset();
                // Reset special request to "None"
                $('#special-request-none').prop('checked', true);
                // Clear selected tables display
                $('#selected-tables-badges').html('');
                $('#selected-tables-display').val('');
                selectedBoxes.clear();
                $('.svg-box').attr('data-value', '0');
                $('rect[id^="rbox-"]').attr('fill', '#539bff');
                $('#div-booking-btn').fadeOut();

                // Reload booked tables
                window.loadBookedTables(window.formatDateForAPI($('#myDate').val()), $('#useZone').val());
            },
            error: function(xhr, status, error) {
                console.error('Error:', error);
                console.error('Response text:', xhr.responseText);

                let errorMessage = 'Failed to save booking. Please try again.';
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    // Use default error message
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: errorMessage
                });
            }
        });
    });

    // Mark tables that are already booked
    window.markBookedTables = function(bookedTables = []) {
        try {
            console.log('Marking booked tables, count:', bookedTables ? bookedTables.length : 0);

            // Ensure bookedTables is an array
            if (!Array.isArray(bookedTables)) {
                console.error('bookedTables is not an array:', bookedTables);
                bookedTables = [];
            }

            // Clear all previous booked table markers
            // This ensures we start fresh when changing dates or tabs
            $('.svg-box').each(function() {
                // Remove booked labels
                $(this).find('.booked-label').remove();
                // Remove data-booked attribute
                $(this).removeAttr('data-booked');
                // Remove booked-disabled class
                $(this).removeClass('booked-disabled');
            });

            try {
                // Reset all tables to default state
                $('.svg-box').removeClass('booked-disabled table-disabled').off('click').on('click', function(event) {
                    try {
                        // Skip if table is already booked or disabled
                        if ($(this).hasClass('booked-disabled') || $(this).hasClass('table-disabled') || $(this).attr('data-booked') === 'true') {
                            console.log('Attempted to click on a booked or disabled table');
                            // Prevent the default action and stop event propagation
                            event.preventDefault();
                            event.stopPropagation();
                            return false;
                        }

                        let $box = $(this);
                        let id = $box.attr('id');
                        if (!id) {
                            console.warn('Box element has no id attribute');
                            return;
                        }

                        let label = id.replace('box-', '');

                        // Handle special characters in selector for tables with slashes
                        let rectSelector;
                        if (label.includes('/')) {
                            // Need to escape the slash for jQuery selector
                            rectSelector = 'rbox-' + label.replace('/', '\\/');
                        } else {
                            rectSelector = 'rbox-' + label;
                        }

                        let $rect = $('#' + rectSelector);
                        if (!$rect.length) {
                            console.warn(`Rectangle element not found: ${rectSelector}`);
                            return;
                        }

                        let data = $box.attr('data-value');

                        if (data === '0') {
                            $box.attr('data-value', '1');
                            $rect.attr('fill', '#00FF00');
                            if (typeof selectedBoxes !== 'undefined' && selectedBoxes.add) {
                                selectedBoxes.add(label);
                            }
                        } else {
                            $box.attr('data-value', '0');
                            $rect.attr('fill', '#539bff');
                            if (typeof selectedBoxes !== 'undefined' && selectedBoxes.delete) {
                                selectedBoxes.delete(label);
                            }
                        }

                        if (typeof toggleBookingButton === 'function') {
                            toggleBookingButton();
                        }
                    } catch (e) {
                        console.error('Error in table click handler:', e);
                    }
                });
            } catch (e) {
                console.error('Error resetting tables to default state:', e);
            }

            try {
                // Reset all rectangles to default color based on their row
                // Reset ALL rectangles since we've already cleared booked status
                $('rect[id^="rbox-"]').each(function() {
                    try {
                        // Get the ID of the rectangle
                        const id = $(this).attr('id');
                        if (!id) {
                            console.warn('Rectangle element has no id attribute');
                            return;
                        }

                        const tableId = id.replace('rbox-', '');

                        // Handle different table ID formats
                        let rowIdentifier;

                        // For new format (A1/1, A2/2, etc.)
                        if (tableId.includes('/')) {
                            rowIdentifier = tableId.split('/')[0]; // A1, A2, etc.
                        }
                        // For previous format (1A01, 2B03, etc.)
                        else if (tableId.length >= 3 && /^\d/.test(tableId)) {
                            rowIdentifier = tableId.substring(0, 2); // 1A, 2B, etc.
                        }
                        // For old format (A1, B2, etc.)
                        else {
                            rowIdentifier = tableId.charAt(0); // A, B, etc.
                        }

                        // Apply the appropriate color based on the row
                        // This uses the colors defined in table-styles.css
                        $(this).attr('fill', '#539bff');
                    } catch (e) {
                        console.error('Error resetting rectangle color:', e);
                    }
                });
            } catch (e) {
                console.error('Error resetting rectangles to default color:', e);
            }

            // Mark booked tables
            bookedTables.forEach(table => {
                try {
                    if (!table) {
                        console.warn('Null or undefined table value');
                        return;
                    }

                    // Convert to string if it's not already
                    const tableStr = table.toString();

                    // Handle special characters in selector for tables with slashes
                    let boxSelector, rectSelector;
                    if (tableStr.includes('/')) {
                        // Need to escape the slash for jQuery selector
                        const escapedTable = tableStr.replace('/', '\\/');
                        boxSelector = `#box-${escapedTable}`;
                        rectSelector = `#rbox-${escapedTable}`;
                    } else {
                        boxSelector = `#box-${tableStr}`;
                        rectSelector = `#rbox-${tableStr}`;
                    }

                    // Debug
                    console.log('Booked Table:', tableStr, 'Box Selector:', boxSelector, 'Rect Selector:', rectSelector);

                    const $box = $(boxSelector);
                    const $rect = $(rectSelector);

                    if ($box.length && $rect.length) {
                        // First remove any existing click handlers
                        $box.off('click');
                        // Then add the booked-disabled class
                        $box.addClass('booked-disabled');
                        // Set the fill color to gray
                        $rect.attr('fill', '#cccccc');
                        // Add a data attribute to mark as booked
                        $box.attr('data-booked', 'true');

                        // Add a 'BOOKED' label to the table
                        if (!$box.find('.booked-label').length) {
                            const $label = $('<div class="booked-label">BOOKED</div>');
                            $box.append($label);
                        }

                        // Log for debugging
                        console.log('Marked table as booked:', tableStr);
                    } else {
                        console.warn(`Table element not found: ${tableStr}`);
                    }
                } catch (e) {
                    console.error('Error marking booked table:', e);
                }
            });
        } catch (e) {
            console.error('Error in markBookedTables function:', e);
        }
    };

    // Mark tables that are disabled
    window.markDisabledTables = function(disabledTables = [], zone) {
        try {
            // Ensure disabledTables is an array
            if (!Array.isArray(disabledTables)) {
                console.error('disabledTables is not an array:', disabledTables);
                disabledTables = [];
            }

            // Ensure zone is a valid number
            let safeZone = parseInt(zone) || 1;
            if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                safeZone = 1;
            }

            console.log('Marking disabled tables for zone:', safeZone, 'Total disabled tables:', disabledTables.length);

            // Filter tables for the current zone
            let zoneTables = [];

            try {
                // Different filtering logic based on floor number
                if (safeZone === 1) {
                    // Floor 1 tables might start with 'A', '1A', or have other formats
                    zoneTables = disabledTables.filter(table => {
                        if (!table || !table.id) return false;

                        const id = table.id.toString();
                        // For the new format (A1/1, A2/1, etc.)
                        if (id.includes('/')) {
                            return id.charAt(0) === 'A' || id.charAt(0) === '1';
                        }
                        // For format with floor number prefix (1A01, etc.)
                        else if (id.length >= 3 && /^\d/.test(id)) {
                            return id.charAt(0) === '1';
                        }
                        // For old format (A1, B2, etc.)
                        else {
                            return ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].includes(id.charAt(0));
                        }
                    });
                } else if (safeZone === 2) {
                    // Floor 2 tables
                    zoneTables = disabledTables.filter(table => {
                        if (!table || !table.id) return false;

                        const id = table.id.toString();
                        // For format with floor number prefix (2B01, etc.)
                        if (id.length >= 3 && /^\d/.test(id)) {
                            return id.charAt(0) === '2';
                        }
                        return false;
                    });
                } else if (safeZone === 3) {
                    // Floor 3 tables
                    zoneTables = disabledTables.filter(table => {
                        if (!table || !table.id) return false;

                        const id = table.id.toString();
                        // For format with floor number prefix (3E01, etc.)
                        if (id.length >= 3 && /^\d/.test(id)) {
                            return id.charAt(0) === '3';
                        }
                        return false;
                    });
                }
            } catch (e) {
                console.error('Error filtering tables for zone:', e);
                zoneTables = [];
            }

            console.log('Filtered', zoneTables.length, 'disabled tables for zone', safeZone);

            // Mark disabled tables
            zoneTables.forEach(table => {
                try {
                    if (!table || !table.id) {
                        console.warn('Invalid table object:', table);
                        return;
                    }

                    const tableId = table.id.toString();

                    // Handle special characters in selector for tables with slashes
                    let boxSelector, rectSelector;
                    if (tableId.includes('/')) {
                        // Need to escape the slash for jQuery selector
                        const escapedTableId = tableId.replace('/', '\\/');
                        boxSelector = `#box-${escapedTableId}`;
                        rectSelector = `#rbox-${escapedTableId}`;
                    } else {
                        boxSelector = `#box-${tableId}`;
                        rectSelector = `#rbox-${tableId}`;
                    }

                    // Debug
                    console.log('Disabled Table:', tableId, 'Box Selector:', boxSelector, 'Rect Selector:', rectSelector);

                    const $box = $(boxSelector);
                    const $rect = $(rectSelector);

                    if ($box.length && $rect.length) {
                        // Skip if already booked
                        if ($box.hasClass('booked-disabled')) {
                            return;
                        }

                        // Add disabled class and remove click handler
                        $box.addClass('table-disabled').off('click');
                        // Set the fill color to gray
                        $rect.attr('fill', '#cccccc');

                        // Create tooltip with disabled info
                        let tooltipText = 'Table disabled';
                        if (table.disabled_until) {
                            try {
                                const disabledDate = new Date(table.disabled_until);
                                if (!isNaN(disabledDate.getTime())) {
                                    const formattedDate = disabledDate.toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'short',
                                        day: 'numeric'
                                    });
                                    tooltipText += ` until ${formattedDate}`;
                                } else {
                                    tooltipText += ' indefinitely';
                                }
                            } catch (e) {
                                console.error('Error formatting disabled date:', e);
                                tooltipText += ' indefinitely';
                            }
                        } else {
                            tooltipText += ' indefinitely';
                        }

                        // Add title attribute for native browser tooltip
                        $box.attr('title', tooltipText);

                        // Add a custom tooltip element
                        try {
                            // Remove any existing tooltip first
                            $box.find('.table-tooltip').remove();

                            const $tooltip = $('<div class="table-tooltip">' + tooltipText + '</div>');
                            $box.append($tooltip);

                            // Show tooltip on hover
                            $box.off('mouseenter mouseleave').on('mouseenter', function() {
                                $(this).find('.table-tooltip').css('display', 'block');
                            }).on('mouseleave', function() {
                                $(this).find('.table-tooltip').css('display', 'none');
                            });
                        } catch (e) {
                            console.error('Error creating tooltip:', e);
                        }
                    } else {
                        console.warn(`Table element not found: ${tableId}`);
                    }
                } catch (e) {
                    console.error('Error marking disabled table:', e);
                }
            });
        } catch (e) {
            console.error('Error in markDisabledTables function:', e);
        }
    };

    // Update floor occupancy percentage and progress bar
    window.updateFloorOccupancy = function(floorNumber, bookedCount) {
        try {
            // Ensure floorNumber is a valid number
            let safeFloorNumber = parseInt(floorNumber) || 1;
            if (isNaN(safeFloorNumber) || safeFloorNumber < 1 || safeFloorNumber > 3) {
                safeFloorNumber = 1;
            }

            // Define total tables for each floor
            const totalTables = {};
            totalTables['1'] = 118; // Updated number of tables on Floor 1 (including 1A01-1A17, 1B01-1B15, 1C01-1C15, 1D01-1D15, 1E01-1E15, 1F01-1F15, 1G01-1G15, 1H01-1H15)
            totalTables['2'] = 160;  // Number of tables on Floor 2
            totalTables['3'] = 200;  // Number of tables on Floor 3

            // Get the progress bar and percentage elements
            const $progressBar = $(`#floor${safeFloorNumber}-progress`);
            const $percentageText = $(`#floor${safeFloorNumber}-percentage`);

            // Handle special case for loading state
            if (bookedCount === -1) {
                // Show loading state
                $percentageText.text('Loading...');
                $progressBar.css('width', '100%')
                           .attr('aria-valuenow', 100)
                           .removeClass('text-bg-primary text-bg-warning text-bg-danger')
                           .addClass('text-bg-secondary');
                return;
            }

            // Ensure bookedCount is a valid number
            let safeBookedCount = parseInt(bookedCount) || 0;
            if (isNaN(safeBookedCount) || safeBookedCount < 0) {
                safeBookedCount = 0;
            }

            // Calculate percentage
            const total = totalTables[safeFloorNumber] || 30;
            const percentage = Math.round((safeBookedCount / total) * 100);

            // Update percentage text - show booked/total format for all floors
            if (safeFloorNumber === 1) {
                $percentageText.text(`${safeBookedCount}/118`);
            } else if (safeFloorNumber === 2) {
                $percentageText.text(`${safeBookedCount}/160`);
            } else if (safeFloorNumber === 3) {
                $percentageText.text(`${safeBookedCount}/200`);
            } else {
                $percentageText.text(`${percentage}%`);
            }

            // Update progress bar width and aria-valuenow
            $progressBar.css('width', `${percentage}%`).attr('aria-valuenow', percentage);

            // Update progress bar color based on occupancy level
            if (percentage >= 80) {
                // Red (text-bg-danger)
                $progressBar.removeClass('text-bg-primary text-bg-warning text-bg-secondary').addClass('text-bg-danger');
            } else if (percentage >= 50) {
                // Yellow (text-bg-warning)
                $progressBar.removeClass('text-bg-primary text-bg-danger text-bg-secondary').addClass('text-bg-warning');
            } else {
                // Blue (text-bg-primary)
                $progressBar.removeClass('text-bg-warning text-bg-danger text-bg-secondary').addClass('text-bg-primary');
            }
        } catch (e) {
            console.error('Error in updateFloorOccupancy:', e);
        }
    };

    // Load booked tables from the server
    window.loadBookedTables = function(date, zone) {
        try {
            // Show loading overlay if this is a direct call (not from loadAllFloors)
            if (!window.updatingAllFloors) {
                window.showLoadingOverlay();
                console.log('Showing loading overlay for direct call to loadBookedTables');
            }

            // Ensure date is properly formatted
            let formattedDate = date;
            if (typeof window.formatDateForAPI === 'function' && date) {
                formattedDate = window.formatDateForAPI(date);
            }

            // Ensure zone is a valid number
            let safeZone = parseInt(zone) || 1;
            if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                safeZone = 1;
            }

            console.log('Loading booked tables for date:', formattedDate, 'zone:', safeZone);

            // First load booked tables
            $.ajax({
                url: 'get_booked_tables.php',
                type: 'GET',
                data: { useDate: formattedDate, useZone: safeZone },
                dataType: 'json',
                success: function(booked) {
                    try {
                        // Ensure booked is an array
                        if (!Array.isArray(booked)) {
                            console.warn('Booked tables response is not an array:', booked);
                            booked = [];
                        }

                        console.log('Loaded', booked.length, 'booked tables for zone', safeZone);

                        // Mark booked tables
                        if (typeof window.markBookedTables === 'function') {
                            window.markBookedTables(booked);
                        } else {
                            console.error('markBookedTables function not defined');
                        }

                        // Then load disabled tables
                        if (typeof window.loadDisabledTables === 'function') {
                            window.loadDisabledTables(safeZone);
                        } else {
                            console.error('loadDisabledTables function not defined');
                        }

                        // Check if all tables on the floor are booked (floor buyout)
                        const totalTablesOnFloor = window.getTotalTablesForFloor(safeZone);
                        const isFloorBuyout = (booked.length >= totalTablesOnFloor * 0.9); // Consider 90% or more as buyout

                        // Add floor buyout indicator if needed
                        if (isFloorBuyout) {
                            window.showFloorBuyoutIndicator(safeZone);
                        } else {
                            window.hideFloorBuyoutIndicator(safeZone);
                        }

                        // Update occupancy percentage for the current floor
                        // Only update if this is called directly (not from loadAllFloors)
                        // This helps avoid duplicate updates
                        if (!window.updatingAllFloors && typeof window.updateFloorOccupancy === 'function') {
                            window.updateFloorOccupancy(safeZone, booked.length);
                            // Hide loading overlay if this is a direct call (not from loadAllFloors)
                            window.hideLoadingOverlay();
                        }
                    } catch (e) {
                        console.error('Error processing booked tables response:', e);
                        // Hide loading overlay on error if this is a direct call
                        if (!window.updatingAllFloors) {
                            window.hideLoadingOverlay();
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading booked tables:', error);
                    console.error('Response text:', xhr.responseText);
                    try {
                        // Hide loading overlay on error if this is a direct call
                        if (!window.updatingAllFloors) {
                            window.hideLoadingOverlay();
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'Failed to load booked tables. Please refresh the page.'
                        });
                    } catch (e) {
                        console.error('Error showing error message:', e);
                        alert('Failed to load booked tables. Please refresh the page.');

                        // Hide loading overlay on error if this is a direct call
                        if (!window.updatingAllFloors) {
                            window.hideLoadingOverlay();
                        }
                    }
                }
            });
        } catch (e) {
            console.error('Error in loadBookedTables function:', e);
            // Hide loading overlay on error if this is a direct call
            if (!window.updatingAllFloors) {
                window.hideLoadingOverlay();
            }
        }
    };

    // Load disabled tables from the server
    window.loadDisabledTables = function(zone) {
        try {
            // Ensure zone is a valid number
            let safeZone = parseInt(zone) || 1;
            if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                safeZone = 1;
            }

            console.log('Loading disabled tables for zone:', safeZone);

            $.ajax({
                url: 'get_disabled_tables.php',
                type: 'GET',
                data: { useZone: safeZone },
                dataType: 'json',
                success: function(disabled) {
                    try {
                        // Ensure disabled is an array
                        if (!Array.isArray(disabled)) {
                            console.warn('Disabled tables response is not an array:', disabled);
                            disabled = [];
                        }

                        console.log('Loaded', disabled.length, 'disabled tables for zone', safeZone);

                        // Mark disabled tables
                        if (typeof window.markDisabledTables === 'function') {
                            window.markDisabledTables(disabled, safeZone);
                        } else {
                            console.error('markDisabledTables function not defined');
                        }
                    } catch (e) {
                        console.error('Error processing disabled tables response:', e);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading disabled tables:', error);
                    console.error('Response text:', xhr.responseText);
                }
            });
        } catch (e) {
            console.error('Error in loadDisabledTables function:', e);
        }
    };

    // Initialize formatDateForAPI function if needed
    try {
        const defaultDate = '<?= date('d-m-Y') ?>';
        console.log('Default date:', defaultDate);

        // Make sure formatDateForAPI is defined before calling it
        if (typeof window.formatDateForAPI !== 'function') {
            console.error('formatDateForAPI function not defined, using fallback');
            // Define a simple fallback if the function is not available
            window.formatDateForAPI = function(dateString) {
                try {
                    // Try to convert dd-mm-yyyy to yyyy-mm-dd
                    if (dateString && dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                        const parts = dateString.split('-');
                        return `${parts[2]}-${parts[1]}-${parts[0]}`;
                    }
                    // Return today's date as a fallback
                    const today = new Date();
                    return today.toISOString().split('T')[0];
                } catch (e) {
                    console.error('Error in fallback formatDateForAPI:', e);
                    // Return today's date as a fallback
                    const today = new Date();
                    return today.toISOString().split('T')[0];
                }
            };
        }

        const initialApiDate = window.formatDateForAPI(defaultDate);
        console.log('Initial API date:', initialApiDate);

        // Make sure loadBookedTables is defined before calling it
        if (typeof window.loadBookedTables !== 'function') {
            console.error('loadBookedTables function not defined, using fallback');
            // Define a simple fallback if the function is not available
            window.loadBookedTables = function(date, zone) {
                console.log('Fallback loadBookedTables called with:', date, zone);
            };
        }

        // Store the initial date and zone for use in loadAllFloors
        window.currentApiDate = initialApiDate;
        window.currentZone = '1';

        // We'll call loadBookedTables from loadAllFloors to avoid duplicate calls
    } catch (e) {
        console.error('Error initializing date functions:', e);
    }

    // Function to get the total number of tables for a floor
    window.getTotalTablesForFloor = function(floor) {
        // Each floor has 8 rows with 15 tables each = 120 tables
        return 120;
    };

    // Function to show floor buyout indicator
    window.showFloorBuyoutIndicator = function(floor) {
        const floorId = `#floor${floor}`;
        const $floorPane = $(floorId);

        // Remove any existing indicator
        $floorPane.find('.floor-buyout-indicator').remove();

        // Add the indicator
        const $indicator = $(`
            <div class="floor-buyout-indicator">
                <div class="floor-buyout-text">Floor Bought Out</div>
            </div>
        `);

        // Append to the floor pane
        $floorPane.find('.simplebar-content').first().append($indicator);

        // Update the tab to show buyout status
        $(`a[href="${floorId}"]`).addClass('text-danger').find('.badge').addClass('bg-danger');
    };

    // Function to hide floor buyout indicator
    window.hideFloorBuyoutIndicator = function(floor) {
        const floorId = `#floor${floor}`;
        const $floorPane = $(floorId);

        // Remove the indicator
        $floorPane.find('.floor-buyout-indicator').remove();

        // Update the tab to remove buyout status
        $(`a[href="${floorId}"]`).removeClass('text-danger').find('.badge').removeClass('bg-danger');
    };

    // Function to reset all table states
    window.resetAllTableStates = function() {
        console.log('Resetting all table states');

        // Clear all booked and disabled states
        $('.svg-box').each(function() {
            // Remove all status classes
            $(this).removeClass('booked-disabled table-disabled');

            // Remove data attributes
            $(this).removeAttr('data-booked');

            // Remove any labels
            $(this).find('.booked-label, .table-tooltip').remove();

            // Reset data-value
            $(this).attr('data-value', '0');
        });

        // Reset all rectangle colors
        $('rect[id^="rbox-"]').attr('fill', '#539bff');

        // Remove floor buyout indicators
        $('.floor-buyout-indicator').remove();
        $('a[href^="#floor"]').removeClass('text-danger').find('.badge').removeClass('bg-danger');

        console.log('All table states have been reset');
    };

    // Function to show loading overlay
    window.showLoadingOverlay = function() {
        $('#loading-overlay').fadeIn(200);
    };

    // Function to hide loading overlay
    window.hideLoadingOverlay = function() {
        $('#loading-overlay').fadeOut(200);
    };

    // Load data for all floors on page load
    window.loadAllFloors = function() {
        try {
            // Show loading overlay
            window.showLoadingOverlay();

            // Reset all table states before loading new data
            window.resetAllTableStates();

            // Set a flag to indicate we're updating all floors
            // This prevents duplicate occupancy updates
            window.updatingAllFloors = true;

            const selectedDate = window.formatDateForAPI($('#myDate').val());
            const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';

            console.log('Loading all floors with date:', selectedDate);

            // First load the current floor's booked tables (this will update the UI)
            window.loadBookedTables(selectedDate, currentFloor);

            // Update occupancy for the current floor
            // We'll handle this separately to ensure it's updated even if the AJAX call is slow
            window.updateFloorOccupancy(parseInt(currentFloor), -1); // -1 means "loading"

            // Then load occupancy data for the other floors without updating UI
            // Only make AJAX calls for floors other than the current one
            let pendingRequests = 0;
            [1, 2, 3].filter(floor => floor.toString() !== currentFloor).forEach(floor => {
                pendingRequests++;
                $.ajax({
                    url: 'get_booked_tables.php',
                    type: 'GET',
                    data: { useDate: selectedDate, useZone: floor },
                    dataType: 'json',
                    success: function(booked) {
                        window.updateFloorOccupancy(floor, booked.length);
                        pendingRequests--;
                        if (pendingRequests === 0) {
                            // All requests completed, reset the flag
                            window.updatingAllFloors = false;
                            // Hide loading overlay
                            window.hideLoadingOverlay();
                        }
                    },
                    error: function() {
                        pendingRequests--;
                        if (pendingRequests === 0) {
                            // All requests completed, reset the flag
                            window.updatingAllFloors = false;
                            // Hide loading overlay
                            window.hideLoadingOverlay();
                        }
                    }
                });
            });

            // If no requests were made, reset the flag immediately
            if (pendingRequests === 0) {
                window.updatingAllFloors = false;
                // Hide loading overlay
                window.hideLoadingOverlay();
            }
        } catch (e) {
            console.error('Error in loadAllFloors:', e);
            window.updatingAllFloors = false; // Reset the flag on error
            window.hideLoadingOverlay(); // Hide loading overlay on error
        }
    };

    // Load all floors data initially
    window.loadAllFloors();

    // Check for existing bookings on the current floor
    const initialFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';
    const currentDate = $('#myDate').val();
    console.log('=== Initial Page Load ===');
    console.log('Initial floor:', initialFloor);
    console.log('Current date:', currentDate);
    console.log('checkExistingBookings function exists:', typeof window.checkExistingBookings);

    if (typeof window.checkExistingBookings === 'function') {
        window.checkExistingBookings(currentDate, initialFloor);
    } else {
        console.error('checkExistingBookings function not defined yet');
    }

    // Load booked tables when date changes
    $('#myDate').off('change').on('change', function () {
        const selectedDate = $(this).val();
        console.log('Date changed to:', selectedDate);

        // Convert date to API format (yyyy-mm-dd)
        const apiDate = window.formatDateForAPI(selectedDate);
        console.log('API formatted date:', apiDate);

        // Store the current date for reference
        window.currentApiDate = apiDate;

        // Get the current floor number
        const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';

        // Check if there are existing bookings for this date and floor
        window.checkExistingBookings(selectedDate, currentFloor);

        // Load data for all floors - this will also load the current floor's booked tables
        window.loadAllFloors();
    });

    // Simplified function - no complex checking needed
    window.checkExistingBookings = function(date, floor) {
        console.log('Checking bookings for date:', date, 'floor:', floor);
        // Button is always enabled - let backend handle validation
    };

    // Simplified button functions (no longer needed but kept for compatibility)
    window.disableBuyoutButton = function(reason) {
        console.log('Button disable requested:', reason);
        // Button stays enabled - let user try and backend will validate
    };

    window.enableBuyoutButton = function() {
        console.log('Button enable requested');
        // Button is always enabled
    };

    // Test function for debugging (simplified)
    // window.testBuyoutButton = function() {
    //     console.log('Testing buyout button...');
    //     const $button = $('#buyout-floor-btn');
    //     console.log('Button exists:', $button.length > 0);
    //     console.log('Button classes:', $button.attr('class'));
    //     $button.click(); // Trigger click
    // };



    // Update tables when tab changes
    $('.nav-link').on('click', function() {
        // Show loading overlay
        window.showLoadingOverlay();

        // Reset all table states before loading new data
        window.resetAllTableStates();

        const floorId = $(this).attr('href').replace('#', '');
        const floorNumber = floorId.replace('floor', '');
        const selectedDate = $('#myDate').val();
        console.log('Tab changed to floor:', floorNumber, 'Selected date:', selectedDate);

        if (floorNumber >= 1 && floorNumber <= 3) {
            // Convert date to API format (yyyy-mm-dd)
            const apiDate = window.formatDateForAPI(selectedDate);
            console.log('API formatted date for tab change:', apiDate);

            // Store the current zone for reference
            window.currentZone = floorNumber;

            // Update the useZone field in the booking form
            $('#useZone').val(floorNumber);

            // Check if there are existing bookings for this date and floor
            window.checkExistingBookings(selectedDate, floorNumber);

            // Load booked tables for the selected floor only
            // We don't need to reload data for other floors since we're just switching tabs
            window.loadBookedTables(apiDate, floorNumber);
        }
    });

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<script>
    $(document).ready(function() {
        
// === CHECK EXISTING BOOKINGS FIRST ===
window.checkExistingBookings = (date, floor) => {
    console.log('Checking bookings for', date, 'floor', floor);
};

// === DOM READY ===
$(function () {
    const $dateInput = $('#myDate');
    const $zoneInput = $('#useZone');
    const selectedBoxes = new Set();

    // Init
    const formatDate = d => new Date(d).toISOString().split('T')[0];
    const currentDate = formatDate($dateInput.val());
    const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';

    // Run initial check
    window.checkExistingBookings(currentDate, currentFloor);

    // Event: Table click
    $('.svg-box').on('click', function () {
        if ($(this).hasClass('booked-disabled')) return;
        const label = this.id.replace('box-', '');
        const rect = $('#rbox-' + label.replace('/', '\/'));
        const selected = $(this).attr('data-value') === '1';
        $(this).attr('data-value', selected ? '0' : '1');
        rect.attr('fill', selected ? '#539bff' : '#00FF00');
        selected ? selectedBoxes.delete(label) : selectedBoxes.add(label);
        $('#div-booking-btn').toggle(selectedBoxes.size > 0);
    });

    // Event: Date change
    $dateInput.on('change', function () {
        const apiDate = formatDate(this.value);
        const floor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';
        window.checkExistingBookings(this.value, floor);
        window.loadBookedTables(apiDate, floor);
    });

    // Book button
    $('#booking-btn').on('click', function () {
        $('#booking-tables').val([...selectedBoxes].join(','));
        $('#bookingModal').modal('show');
    });

    // Submit booking
    $('#submit-booking').on('click', function () {
        if (!$('#bookingForm')[0].checkValidity()) return $('#bookingForm')[0].reportValidity();
        Swal.fire({ title: 'Saving...', didOpen: () => Swal.showLoading() });
        $.post('booking.php', $('#bookingForm').serialize(), res => {
            Swal.fire('Success', 'Booking saved!', 'success');
            $('#bookingModal').modal('hide');
        }).fail(xhr => {
            Swal.fire('Error', xhr.responseText || 'Failed to book');
        });
    });
});

    });
</script>
