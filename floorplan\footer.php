
<!-- Core JavaScript Libraries -->
<script src="../assets/js/vendor.min.js"></script>
<script src="../assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="../assets/libs/simplebar/dist/simplebar.min.js"></script>

<!-- Theme Scripts -->
<script src="../assets/js/theme/app.init.js"></script>
<script src="../assets/js/theme/theme.js"></script>
<script src="../assets/js/theme/app.min.js"></script>
<script src="../assets/js/theme/sidebarmenu.js"></script>

<!-- External CDN Libraries -->
<script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

<!-- Application Scripts -->
<script src="js/table-naming.js"></script>
<script src="js/datepicker-init.js?v=1.0.2"></script>
<script src="js/load-agents.js"></script>

<!-- Floor Buyout Functionality -->
<script>
$(document).ready(function() {
    'use strict';

    console.log('Floor buyout functionality initialized');

    /**
     * Floor Buyout Modal Handler
     */
    const FloorBuyout = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $('#buyout-floor-btn').on('click', this.openModal.bind(this));
            $('#submit-buyout').on('click', this.submitBuyout.bind(this));
        },

        openModal: function() {
            console.log('Opening floor buyout modal');

            const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';
            const selectedDate = $('#myDate').val();

            $('#buyout-useZone').val(currentFloor);
            $('#buyout-useDate').val(selectedDate);
            $('#buyoutFloorModal').modal('show');
        },

        submitBuyout: function() {
            if (!this.validateForm()) return;

            const formData = this.collectFormData();
            this.showLoadingIndicator();
            this.sendBuyoutRequest(formData);
        },

        validateForm: function() {
            const form = $('#buyoutFloorForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return false;
            }
            return true;
        },

        collectFormData: function() {
            const rawDate = $('#buyout-useDate').val();
            const formattedDate = this.formatDateForAPI(rawDate);

            return {
                customer: $('#buyout-customer').val(),
                phone: $('#buyout-phone').val(),
                voucher: $('#buyout-voucher').val() || '',
                agent: $('#buyout-agent').val() || '',
                amount: $('#buyout-amount').val() || '0',
                remark: $('#buyout-remark').val() || 'Entire Floor Booking',
                specialRequest: $('input[name="specialRequest"]:checked').val() || '0',
                paymentType: $('input[name="buyoutPaymentType"]:checked').val() || 'Cash',
                useDate: formattedDate,
                useZone: $('#buyout-useZone').val()
            };
        },

        formatDateForAPI: function(dateString) {
            try {
                if (!dateString) {
                    const today = new Date();
                    return today.toISOString().split('T')[0];
                }

                // If already in YYYY-MM-DD format, return as is
                if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                    return dateString;
                }

                // Convert DD-MM-YYYY to YYYY-MM-DD
                if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                    const parts = dateString.split('-');
                    return `${parts[2]}-${parts[1]}-${parts[0]}`;
                }

                // Fallback to today's date
                const today = new Date();
                return today.toISOString().split('T')[0];
            } catch (e) {
                console.error('Error formatting date:', e);
                const today = new Date();
                return today.toISOString().split('T')[0];
            }
        },

        showLoadingIndicator: function() {
            Swal.fire({
                title: 'Booking entire floor...',
                text: 'Please wait',
                allowOutsideClick: false,
                didOpen: () => Swal.showLoading()
            });
        },

        sendBuyoutRequest: function(formData) {
            $.ajax({
                url: 'buyout_floor.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: this.handleSuccess.bind(this),
                error: this.handleError.bind(this)
            });
        },

        handleSuccess: function(response) {
            console.log('Floor buyout successful:', response);

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Entire floor booked successfully!',
                timer: 2000
            });

            this.resetModal();
            setTimeout(() => location.reload(), 2000);
        },

        handleError: function(xhr, status, error) {
            console.error('Floor buyout error:', error, xhr.responseText);

            let errorMessage = 'Failed to book entire floor. Please try again.';
            try {
                const errorResponse = JSON.parse(xhr.responseText);
                if (errorResponse.message) {
                    errorMessage = errorResponse.message;
                }
            } catch (e) {
                console.error('Error parsing response:', e);
            }

            Swal.fire({
                icon: 'error',
                title: 'Booking Failed',
                text: errorMessage
            });
        },

        resetModal: function() {
            $('#buyoutFloorModal').modal('hide');
            $('#buyoutFloorForm')[0].reset();
        }
    };

    // Initialize Floor Buyout functionality
    FloorBuyout.init();
});
</script>
<?php
// Get current date for initialization
$today = date('Y-m-d');
?>

<!-- Main Floorplan Application -->
<script>
$(document).ready(function() {
    'use strict';

    /**
     * Floorplan Application Main Controller
     */
    const FloorplanApp = {
        selectedBoxes: new Set(),

        init: function() {
            this.initializeDatePicker();
            this.bindEvents();
            this.setupUtilityFunctions();
        },

        initializeDatePicker: function() {
            const today = new Date();
            const formattedDate = today.getDate().toString().padStart(2, '0') + '-' +
                                 (today.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                 today.getFullYear();
            $('#useDate').val(formattedDate);
        },

        /**
         * Date Formatting Utilities
         */
        formatDateForDisplay: function(dateString) {
            try {
                if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                    return dateString;
                }

                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    console.error('Invalid date:', dateString);
                    return dateString;
                }

                return date.toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                }).replace(/\//g, '-');
            } catch (e) {
                console.error('Error formatting date for display:', e);
                return dateString;
            }
        },

        formatFullDate: function(dateString) {
            try {
                let date;
                if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                    const parts = dateString.split('-');
                    date = new Date(parts[2], parts[1] - 1, parts[0]);
                } else {
                    date = new Date(dateString);
                }

                if (isNaN(date.getTime())) {
                    console.error('Invalid date:', dateString);
                    return dateString;
                }

                return date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric'
                });
            } catch (e) {
                console.error('Error formatting full date:', e);
                return dateString;
            }
        },

        setupUtilityFunctions: function() {
            // Define formatDateForAPI as a global function if it doesn't exist yet
            if (typeof window.formatDateForAPI !== 'function') {
                window.formatDateForAPI = function(dateString) {
                    try {
                        if (!dateString) {
                            const today = new Date();
                            return today.toISOString().split('T')[0];
                        }

                        if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                            return dateString;
                        }

                        if (dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                            const parts = dateString.split('-');
                            return `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }

                        if (dateString.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                            const parts = dateString.split('/');
                            return `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }

                        const date = new Date(dateString);
                        if (isNaN(date.getTime())) {
                            console.error('Invalid date for API:', dateString);
                            const today = new Date();
                            return today.toISOString().split('T')[0];
                        }
                        return date.toISOString().split('T')[0];
                    } catch (e) {
                        console.error('Error formatting date for API:', e);
                        const today = new Date();
                        return today.toISOString().split('T')[0];
                    }
                };
            }
        },

        preventBlockUIInterference: function() {
            console.log('Preventing block-ui interference with submit button');

            // Remove block-ui event handlers from submit button if they exist
            $('#submit-booking').off('click.blockUI');

            // Ensure submit button doesn't have block-related classes
            $('#submit-booking').removeClass('block-default block-card block-sidenav onblock onunblock');

            // If block-ui plugin is loaded, unbind its event handlers for our button
            if (typeof $.blockUI !== 'undefined') {
                console.log('Block UI plugin detected - removing conflicting handlers');
                // Remove any existing block-ui handlers that might interfere
                $(document).off('click.blockUI', '#submit-booking');
            }
        },

        bindEvents: function() {
            // Prevent block-ui plugin interference
            this.preventBlockUIInterference();

            // Date change handler
            $('#myDate').on('change', this.handleDateChange.bind(this));

            // Table selection handler
            $('.svg-box').on('click', this.handleTableClick.bind(this));

            // Booking button handler
            $('#booking-btn').on('click', this.openBookingModal.bind(this));

            // Submit booking handler - prevent block-default interference
            $('#submit-booking').on('click', function(e) {
                // Stop event propagation to prevent block-ui from catching it
                e.stopImmediatePropagation();
                // Remove any block-related classes that might interfere
                $(this).removeClass('block-default block-card block-sidenav');
                // Call the actual submit booking function
                FloorplanApp.submitBooking.call(FloorplanApp, e);
            });

            // Modal close event handler - re-enable submit button when modal is closed
            $('#bookingModal').on('hidden.bs.modal', this.enableSubmitButton.bind(this));

            // Initialize date display
            this.initializeDateDisplay();
        },

        handleDateChange: function() {
            const selectedDate = $('#myDate').val();
            $('#shortDate').text(this.formatDateForDisplay(selectedDate));
            $('#fullDate').text(this.formatFullDate(selectedDate));
            $('#useDate').val(selectedDate);

            // Reset table selections
            this.selectedBoxes.clear();
            $('.svg-box').attr('data-value', '0');
            $('rect[id^="rbox-"]').attr('fill', '#539bff');
            $('#div-booking-btn').fadeOut();

            // Load booked tables for the selected date
            window.loadBookedTables(window.formatDateForAPI(selectedDate), $('#useZone').val());
        },

        initializeDateDisplay: function() {
            const initialDate = $('#myDate').val();
            $('#shortDate').text(this.formatDateForDisplay(initialDate));
            $('#fullDate').text(this.formatFullDate(initialDate));

            // Ensure date display is properly initialized
            if ($('#shortDate').text() === '') {
                $('#shortDate').text(this.formatDateForDisplay(initialDate));
                $('#fullDate').text(this.formatFullDate(initialDate));
            }
        },

        /**
         * Table Selection Handler
         */
        handleTableClick: function(event) {
            const $box = $(event.currentTarget);

            // Skip if table is already booked or disabled
            if ($box.hasClass('booked-disabled') || $box.hasClass('table-disabled')) {
                return;
            }

            const id = $box.attr('id');
            const label = id.replace('box-', '');
            const rectSelector = this.getRectSelector(label);
            const $rect = $('#' + rectSelector);
            const data = $box.attr('data-value');

            if (data === '0') {
                this.selectTable($box, $rect, label);
            } else {
                this.deselectTable($box, $rect, label);
            }

            this.toggleBookingButton();
            this.updateAdultCount();
        },

        getRectSelector: function(label) {
            if (label.includes('/')) {
                return 'rbox-' + label.replace('/', '\\/');
            }
            return 'rbox-' + label;
        },

        selectTable: function($box, $rect, label) {
            $box.attr('data-value', '1');
            $rect.attr('fill', '#00FF00');
            this.selectedBoxes.add(label);
        },

        deselectTable: function($box, $rect, label) {
            $box.attr('data-value', '0');
            $rect.attr('fill', '#539bff');
            this.selectedBoxes.delete(label);
        },

        toggleBookingButton: function() {
            if (this.selectedBoxes.size > 0) {
                $('#div-booking-btn').fadeIn();
            } else {
                $('#div-booking-btn').fadeOut();
            }
        },

        updateAdultCount: function() {
            if ($('#bookingModal').hasClass('show')) {
                const adultCount = this.selectedBoxes.size * 2;
                $('input[name="adult"]').val(adultCount);
            }
        },

        /**
         * Booking Modal Handler
         */
        openBookingModal: function() {
            const selected = Array.from(this.selectedBoxes);
            const selectedDate = $('#myDate').val();
            const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';

            // Update selected tables display
            $('#selected-tables-display').val(selected.join(','));
            $('#booking-tables').val(selected.join(','));

            // Create badges for selected tables
            const badgesHtml = selected.map(table =>
                `<span class="badge bg-primary me-1 mb-1">${table}</span>`
            ).join('');
            $('#selected-tables-badges').html(badgesHtml);

            // Calculate adults automatically (number of tables × 2)
            const adultCount = selected.length * 2;
            $('input[name="adult"]').val(adultCount);

            $('#useDate').val(selectedDate);
            $('#useZone').val(currentFloor);
            $('#bookingModal').modal('show');
        },

        /**
         * Booking Submission Handler
         */
        submitBooking: function() {
            if (!this.validateBookingForm()) return;

            // Disable the submit button and show waiting status
            this.disableSubmitButton();

            const formData = this.prepareBookingData();
            this.showBookingLoadingIndicator();
            this.sendBookingRequest(formData);
        },

        disableSubmitButton: function() {
            const submitBtn = $('#submit-booking');
            const originalText = submitBtn.text();

            // Store original text for later restoration
            submitBtn.data('original-text', originalText);

            // Disable button and show waiting status
            submitBtn.prop('disabled', true)
                     .html('<i class="fas fa-spinner fa-spin me-2"></i>Please wait...')
                     .addClass('btn-secondary')
                     .removeClass('btn-primary');
        },

        enableSubmitButton: function() {
            const submitBtn = $('#submit-booking');
            const originalText = submitBtn.data('original-text') || 'Submit Booking';

            // Re-enable button and restore original text
            submitBtn.prop('disabled', false)
                     .html(originalText)
                     .removeClass('btn-secondary')
                     .addClass('btn-primary');
        },

        validateBookingForm: function() {
            const form = $('#bookingForm')[0];
            if (!form.checkValidity()) {
                form.reportValidity();
                return false;
            }
            return true;
        },

        prepareBookingData: function() {
            const dateValue = $('#useDate').val();
            const apiFormattedDate = window.formatDateForAPI(dateValue);
            const useZone = $('#useZone').val();
            const tables = $('#booking-tables').val();

            console.log('Booking data:', {
                originalDate: dateValue,
                apiDate: apiFormattedDate,
                floor: useZone,
                tables: tables
            });

            return $('#bookingForm').serialize() +
                   '&useDate=' + encodeURIComponent(apiFormattedDate) +
                   '&useZone=' + encodeURIComponent(useZone) +
                   '&tables=' + encodeURIComponent(tables);
        },

        showBookingLoadingIndicator: function() {
            Swal.fire({
                title: 'Saving booking...',
                text: 'Please wait',
                allowOutsideClick: false,
                didOpen: () => Swal.showLoading()
            });
        },

        sendBookingRequest: function(formData) {
            $.ajax({
                url: 'booking.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: this.handleBookingSuccess.bind(this),
                error: this.handleBookingError.bind(this)
            });
        },

        handleBookingSuccess: function(response) {
            console.log('Booking successful:', response);

            // Re-enable the submit button
            this.enableSubmitButton();

            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Booking saved successfully!'
            });

            this.resetBookingModal();
            this.reloadBookedTables();
        },

        handleBookingError: function(xhr, status, error) {
            console.error('Booking error:', error, xhr.responseText);

            // Re-enable the submit button
            this.enableSubmitButton();

            let errorMessage = 'Failed to save booking. Please try again.';
            try {
                const errorResponse = JSON.parse(xhr.responseText);
                if (errorResponse.message) {
                    errorMessage = errorResponse.message;
                }
            } catch (e) {
                console.error('Error parsing response:', e);
            }

            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: errorMessage
            });
        },

        resetBookingModal: function() {
            $('#bookingModal').modal('hide');
            $('#bookingForm')[0].reset();
            $('#special-request-none').prop('checked', true);
            $('#selected-tables-badges').html('');
            $('#selected-tables-display').val('');

            // Re-enable the submit button
            this.enableSubmitButton();

            this.selectedBoxes.clear();
            $('.svg-box').attr('data-value', '0');
            $('rect[id^="rbox-"]').attr('fill', '#539bff');
            $('#div-booking-btn').fadeOut();
        },

        reloadBookedTables: function() {
            window.loadBookedTables(
                window.formatDateForAPI($('#myDate').val()),
                $('#useZone').val()
            );
        }

    };

    // Initialize the FloorplanApp
    FloorplanApp.init();

    /**
     * Table Management Functions
     */
    window.TableManager = {
        /**
         * Mark tables that are already booked
         */
        markBookedTables: function(bookedTables = []) {
            try {
                console.log('Marking booked tables, count:', bookedTables ? bookedTables.length : 0);

                if (!Array.isArray(bookedTables)) {
                    console.error('bookedTables is not an array:', bookedTables);
                    bookedTables = [];
                }

                this.clearPreviousBookedMarkers();
                this.resetTablesToDefaultState();
                this.resetRectanglesToDefaultColor();
                this.markTablesAsBooked(bookedTables);
            } catch (e) {
                console.error('Error in markBookedTables function:', e);
            }
        },

        clearPreviousBookedMarkers: function() {
            $('.svg-box').each(function() {
                $(this).find('.booked-label').remove();
                $(this).removeAttr('data-booked');
                $(this).removeClass('booked-disabled');
            });
        },

        resetTablesToDefaultState: function() {
            try {
                $('.svg-box').removeClass('booked-disabled table-disabled').off('click');
                // Re-bind click events using FloorplanApp's handler
                $('.svg-box').on('click', FloorplanApp.handleTableClick.bind(FloorplanApp));
            } catch (e) {
                console.error('Error resetting tables to default state:', e);
            }
        },

        resetRectanglesToDefaultColor: function() {
            try {
                $('rect[id^="rbox-"]').each(function() {
                    try {
                        const id = $(this).attr('id');
                        if (!id) {
                            console.warn('Rectangle element has no id attribute');
                            return;
                        }
                        $(this).attr('fill', '#539bff');
                    } catch (e) {
                        console.error('Error resetting rectangle color:', e);
                    }
                });
            } catch (e) {
                console.error('Error resetting rectangles to default color:', e);
            }
        },

        markTablesAsBooked: function(bookedTables) {
            bookedTables.forEach(table => {
                try {
                    if (!table) {
                        console.warn('Null or undefined table value');
                        return;
                    }

                    const tableStr = table.toString();
                    const selectors = this.getTableSelectors(tableStr);
                    const $box = $(selectors.box);
                    const $rect = $(selectors.rect);

                    if ($box.length && $rect.length) {
                        this.markSingleTableAsBooked($box, $rect, tableStr);
                    } else {
                        console.warn(`Table element not found: ${tableStr}`);
                    }
                } catch (e) {
                    console.error('Error marking booked table:', e);
                }
            });
        },

        getTableSelectors: function(tableStr) {
            if (tableStr.includes('/')) {
                const escapedTable = tableStr.replace('/', '\\/');
                return {
                    box: `#box-${escapedTable}`,
                    rect: `#rbox-${escapedTable}`
                };
            }
            return {
                box: `#box-${tableStr}`,
                rect: `#rbox-${tableStr}`
            };
        },

        markSingleTableAsBooked: function($box, $rect, tableStr) {
            $box.off('click');
            $box.addClass('booked-disabled');
            $rect.attr('fill', '#cccccc');
            $box.attr('data-booked', 'true');

            if (!$box.find('.booked-label').length) {
                const $label = $('<div class="booked-label">BOOKED</div>');
                $box.append($label);
            }

            console.log('Marked table as booked:', tableStr);
        },

        /**
         * Mark tables that are disabled
         */
        markDisabledTables: function(disabledTables = [], zone) {
            try {
                if (!Array.isArray(disabledTables)) {
                    console.error('disabledTables is not an array:', disabledTables);
                    disabledTables = [];
                }

                const safeZone = this.validateZone(zone);
                console.log('Marking disabled tables for zone:', safeZone, 'Total disabled tables:', disabledTables.length);

                const zoneTables = this.filterTablesForZone(disabledTables, safeZone);
                console.log('Filtered', zoneTables.length, 'disabled tables for zone', safeZone);

                this.markTablesAsDisabled(zoneTables);
            } catch (e) {
                console.error('Error in markDisabledTables function:', e);
            }
        },

        validateZone: function(zone) {
            let safeZone = parseInt(zone) || 1;
            if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                safeZone = 1;
            }
            return safeZone;
        },

        filterTablesForZone: function(disabledTables, safeZone) {
            try {
                const zoneFilters = {
                    1: (table) => {
                        if (!table || !table.id) return false;
                        const id = table.id.toString();

                        if (id.includes('/')) {
                            return id.charAt(0) === 'A' || id.charAt(0) === '1';
                        } else if (id.length >= 3 && /^\d/.test(id)) {
                            return id.charAt(0) === '1';
                        } else {
                            return ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].includes(id.charAt(0));
                        }
                    },
                    2: (table) => {
                        if (!table || !table.id) return false;
                        const id = table.id.toString();
                        return id.length >= 3 && /^\d/.test(id) && id.charAt(0) === '2';
                    },
                    3: (table) => {
                        if (!table || !table.id) return false;
                        const id = table.id.toString();
                        return id.length >= 3 && /^\d/.test(id) && id.charAt(0) === '3';
                    }
                };

                return disabledTables.filter(zoneFilters[safeZone] || (() => false));
            } catch (e) {
                console.error('Error filtering tables for zone:', e);
                return [];
            }
        },

        markTablesAsDisabled: function(zoneTables) {
            zoneTables.forEach(table => {
                try {
                    if (!table || !table.id) {
                        console.warn('Invalid table object:', table);
                        return;
                    }

                    const tableId = table.id.toString();
                    const selectors = this.getTableSelectors(tableId);
                    const $box = $(selectors.box);
                    const $rect = $(selectors.rect);

                    if ($box.length && $rect.length) {
                        if (!$box.hasClass('booked-disabled')) {
                            this.markSingleTableAsDisabled($box, $rect, table);
                        }
                    } else {
                        console.warn(`Table element not found: ${tableId}`);
                    }
                } catch (e) {
                    console.error('Error marking disabled table:', e);
                }
            });
        },

        markSingleTableAsDisabled: function($box, $rect, table) {
            $box.addClass('table-disabled').off('click');
            $rect.attr('fill', '#cccccc');

            const tooltipText = this.createDisabledTooltipText(table);
            $box.attr('title', tooltipText);

            this.addCustomTooltip($box, tooltipText);
        },

        createDisabledTooltipText: function(table) {
            let tooltipText = 'Table disabled';

            if (table.disabled_until) {
                try {
                    const disabledDate = new Date(table.disabled_until);
                    if (!isNaN(disabledDate.getTime())) {
                        const formattedDate = disabledDate.toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                        });
                        tooltipText += ` until ${formattedDate}`;
                    } else {
                        tooltipText += ' indefinitely';
                    }
                } catch (e) {
                    console.error('Error formatting disabled date:', e);
                    tooltipText += ' indefinitely';
                }
            } else {
                tooltipText += ' indefinitely';
            }

            return tooltipText;
        },

        addCustomTooltip: function($box, tooltipText) {
            try {
                $box.find('.table-tooltip').remove();

                const $tooltip = $('<div class="table-tooltip">' + tooltipText + '</div>');
                $box.append($tooltip);

                $box.off('mouseenter mouseleave').on('mouseenter', function() {
                    $(this).find('.table-tooltip').css('display', 'block');
                }).on('mouseleave', function() {
                    $(this).find('.table-tooltip').css('display', 'none');
                });
            } catch (e) {
                console.error('Error creating tooltip:', e);
            }
        }
    };

    /**
     * Floor Occupancy Management
     */
    window.FloorOccupancy = {
        totalTables: {
            '1': 118, // Floor 1 tables
            '2': 160, // Floor 2 tables
            '3': 200  // Floor 3 tables
        },

        updateFloorOccupancy: function(floorNumber, bookedCount) {
            try {
                const safeFloorNumber = this.validateFloorNumber(floorNumber);
                const $progressBar = $(`#floor${safeFloorNumber}-progress`);
                const $percentageText = $(`#floor${safeFloorNumber}-percentage`);

                if (bookedCount === -1) {
                    this.showLoadingState($progressBar, $percentageText);
                    return;
                }

                const safeBookedCount = this.validateBookedCount(bookedCount);
                const percentage = this.calculatePercentage(safeBookedCount, safeFloorNumber);

                this.updateDisplay($progressBar, $percentageText, safeFloorNumber, safeBookedCount, percentage);
                this.updateProgressBarColor($progressBar, percentage);
            } catch (e) {
                console.error('Error in updateFloorOccupancy:', e);
            }
        },

        validateFloorNumber: function(floorNumber) {
            let safeFloorNumber = parseInt(floorNumber) || 1;
            if (isNaN(safeFloorNumber) || safeFloorNumber < 1 || safeFloorNumber > 3) {
                safeFloorNumber = 1;
            }
            return safeFloorNumber;
        },

        validateBookedCount: function(bookedCount) {
            let safeBookedCount = parseInt(bookedCount) || 0;
            if (isNaN(safeBookedCount) || safeBookedCount < 0) {
                safeBookedCount = 0;
            }
            return safeBookedCount;
        },

        calculatePercentage: function(bookedCount, floorNumber) {
            const total = this.totalTables[floorNumber] || 30;
            return Math.round((bookedCount / total) * 100);
        },

        showLoadingState: function($progressBar, $percentageText) {
            $percentageText.text('Loading...');
            $progressBar.css('width', '100%')
                       .attr('aria-valuenow', 100)
                       .removeClass('text-bg-primary text-bg-warning text-bg-danger')
                       .addClass('text-bg-secondary');
        },

        updateDisplay: function($progressBar, $percentageText, floorNumber, bookedCount, percentage) {
            const displayText = `${bookedCount}/${this.totalTables[floorNumber]}`;
            $percentageText.text(displayText);
            $progressBar.css('width', `${percentage}%`).attr('aria-valuenow', percentage);
        },

        updateProgressBarColor: function($progressBar, percentage) {
            $progressBar.removeClass('text-bg-primary text-bg-warning text-bg-danger text-bg-secondary');

            if (percentage >= 80) {
                $progressBar.addClass('text-bg-danger');
            } else if (percentage >= 50) {
                $progressBar.addClass('text-bg-warning');
            } else {
                $progressBar.addClass('text-bg-primary');
            }
        }
    };

    /**
     * Data Loading Manager
     */
    window.DataLoader = {
        loadBookedTables: function(date, zone) {
            try {
                if (!window.updatingAllFloors) {
                    window.showLoadingOverlay();
                }

                const formattedDate = this.formatDate(date);
                const safeZone = this.validateZone(zone);

                console.log('Loading booked tables for date:', formattedDate, 'zone:', safeZone);

                $.ajax({
                    url: 'get_booked_tables.php',
                    type: 'GET',
                    data: { useDate: formattedDate, useZone: safeZone },
                    dataType: 'json',
                    success: this.handleBookedTablesSuccess.bind(this, safeZone),
                    error: this.handleBookedTablesError.bind(this)
                });
            } catch (e) {
                console.error('Error in loadBookedTables function:', e);
                this.hideLoadingOverlayIfNeeded();
            }
        },

        formatDate: function(date) {
            if (typeof window.formatDateForAPI === 'function' && date) {
                return window.formatDateForAPI(date);
            }
            return date;
        },

        validateZone: function(zone) {
            let safeZone = parseInt(zone) || 1;
            if (isNaN(safeZone) || safeZone < 1 || safeZone > 3) {
                safeZone = 1;
            }
            return safeZone;
        },

        handleBookedTablesSuccess: function(safeZone, booked) {
            try {
                if (!Array.isArray(booked)) {
                    console.warn('Booked tables response is not an array:', booked);
                    booked = [];
                }

                console.log('Loaded', booked.length, 'booked tables for zone', safeZone);

                // Mark booked tables
                if (window.TableManager && window.TableManager.markBookedTables) {
                    window.TableManager.markBookedTables(booked);
                }

                // Load disabled tables
                this.loadDisabledTables(safeZone);

                // Handle floor buyout indicator
                this.handleFloorBuyoutIndicator(safeZone, booked);

                // Update occupancy if not updating all floors
                if (!window.updatingAllFloors) {
                    if (window.FloorOccupancy && window.FloorOccupancy.updateFloorOccupancy) {
                        window.FloorOccupancy.updateFloorOccupancy(safeZone, booked.length);
                    }
                    window.hideLoadingOverlay();
                }
            } catch (e) {
                console.error('Error processing booked tables response:', e);
                this.hideLoadingOverlayIfNeeded();
            }
        },

        handleBookedTablesError: function(xhr, status, error) {
            console.error('Error loading booked tables:', error, xhr.responseText);

            this.hideLoadingOverlayIfNeeded();

            try {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to load booked tables. Please refresh the page.'
                });
            } catch (e) {
                console.error('Error showing error message:', e);
                alert('Failed to load booked tables. Please refresh the page.');
            }
        },

        handleFloorBuyoutIndicator: function(safeZone, booked) {
            const totalTablesOnFloor = window.getTotalTablesForFloor ? window.getTotalTablesForFloor(safeZone) : 120;
            const isFloorBuyout = (booked.length >= totalTablesOnFloor * 0.9);

            if (isFloorBuyout && window.showFloorBuyoutIndicator) {
                window.showFloorBuyoutIndicator(safeZone);
            } else if (window.hideFloorBuyoutIndicator) {
                window.hideFloorBuyoutIndicator(safeZone);
            }
        },

        hideLoadingOverlayIfNeeded: function() {
            if (!window.updatingAllFloors && window.hideLoadingOverlay) {
                window.hideLoadingOverlay();
            }
        },

        loadDisabledTables: function(zone) {
            try {
                const safeZone = this.validateZone(zone);
                console.log('Loading disabled tables for zone:', safeZone);

                $.ajax({
                    url: 'get_disabled_tables.php',
                    type: 'GET',
                    data: { useZone: safeZone },
                    dataType: 'json',
                    success: this.handleDisabledTablesSuccess.bind(this, safeZone),
                    error: this.handleDisabledTablesError.bind(this)
                });
            } catch (e) {
                console.error('Error in loadDisabledTables function:', e);
            }
        },

        handleDisabledTablesSuccess: function(safeZone, disabled) {
            try {
                if (!Array.isArray(disabled)) {
                    console.warn('Disabled tables response is not an array:', disabled);
                    disabled = [];
                }

                console.log('Loaded', disabled.length, 'disabled tables for zone', safeZone);

                if (window.TableManager && window.TableManager.markDisabledTables) {
                    window.TableManager.markDisabledTables(disabled, safeZone);
                }
            } catch (e) {
                console.error('Error processing disabled tables response:', e);
            }
        },

        handleDisabledTablesError: function(xhr, status, error) {
            console.error('Error loading disabled tables:', error, xhr.responseText);
        }
    };

    /**
     * Utility Functions and Initialization
     */
    window.UtilityFunctions = {
        initializeDateFunctions: function() {
            try {
                const defaultDate = '<?= date('d-m-Y') ?>';
                console.log('Default date:', defaultDate);

                this.ensureFormatDateForAPI();
                this.ensureLoadBookedTables();

                const initialApiDate = window.formatDateForAPI(defaultDate);
                console.log('Initial API date:', initialApiDate);

                window.currentApiDate = initialApiDate;
                window.currentZone = '1';
            } catch (e) {
                console.error('Error initializing date functions:', e);
            }
        },

        ensureFormatDateForAPI: function() {
            if (typeof window.formatDateForAPI !== 'function') {
                console.error('formatDateForAPI function not defined, using fallback');
                window.formatDateForAPI = function(dateString) {
                    try {
                        if (dateString && dateString.match(/^\d{2}-\d{2}-\d{4}$/)) {
                            const parts = dateString.split('-');
                            return `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                        const today = new Date();
                        return today.toISOString().split('T')[0];
                    } catch (e) {
                        console.error('Error in fallback formatDateForAPI:', e);
                        const today = new Date();
                        return today.toISOString().split('T')[0];
                    }
                };
            }
        },

        ensureLoadBookedTables: function() {
            if (typeof window.loadBookedTables !== 'function') {
                console.error('loadBookedTables function not defined, using fallback');
                window.loadBookedTables = function(date, zone) {
                    console.log('Fallback loadBookedTables called with:', date, zone);
                };
            }
        }
    };

    /**
     * Floor Management Utilities
     */
    window.FloorUtils = {
        getTotalTablesForFloor: function(floor) {
            return 200; // Each floor has 8 rows with 15 tables each
        },

        showFloorBuyoutIndicator: function(floor) {
            const floorId = `#floor${floor}`;
            const $floorPane = $(floorId);

            $floorPane.find('.floor-buyout-indicator').remove();

            const $indicator = $(`
                <div class="floor-buyout-indicator">
                    <div class="floor-buyout-text">The entire floor is reserved.</div>
                </div>
            `);

            $floorPane.find('.simplebar-content').first().append($indicator);
            $(`a[href="${floorId}"]`).addClass('text-danger').find('.badge').addClass('bg-danger');
        },

        hideFloorBuyoutIndicator: function(floor) {
            const floorId = `#floor${floor}`;
            const $floorPane = $(floorId);

            $floorPane.find('.floor-buyout-indicator').remove();
            $(`a[href="${floorId}"]`).removeClass('text-danger').find('.badge').removeClass('bg-danger');
        },

        resetAllTableStates: function() {
            console.log('Resetting all table states');

            $('.svg-box').each(function() {
                $(this).removeClass('booked-disabled table-disabled')
                       .removeAttr('data-booked')
                       .attr('data-value', '0');
                $(this).find('.booked-label, .table-tooltip').remove();
            });

            $('rect[id^="rbox-"]').attr('fill', '#539bff');

            $('.floor-buyout-indicator').remove();
            $('a[href^="#floor"]').removeClass('text-danger').find('.badge').removeClass('bg-danger');

            console.log('All table states have been reset');
        },

        showLoadingOverlay: function() {
            $('#loading-overlay').fadeIn(200);
        },

        hideLoadingOverlay: function() {
            $('#loading-overlay').fadeOut(200);
        }
    };

    /**
     * Main Application Controller
     */
    window.FloorplanController = {
        loadAllFloors: function() {
            try {
                window.FloorUtils.showLoadingOverlay();
                window.FloorUtils.resetAllTableStates();
                window.updatingAllFloors = true;

                const selectedDate = window.formatDateForAPI($('#myDate').val());
                const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';

                console.log('Loading all floors with date:', selectedDate);

                // Load current floor data
                if (window.DataLoader) {
                    window.DataLoader.loadBookedTables(selectedDate, currentFloor);
                }

                // Update current floor occupancy to loading state
                if (window.FloorOccupancy) {
                    window.FloorOccupancy.updateFloorOccupancy(parseInt(currentFloor), -1);
                }

                // Load other floors' occupancy data
                this.loadOtherFloorsOccupancy(selectedDate, currentFloor);
            } catch (e) {
                console.error('Error in loadAllFloors:', e);
                this.resetLoadingState();
            }
        },

        loadOtherFloorsOccupancy: function(selectedDate, currentFloor) {
            let pendingRequests = 0;
            const otherFloors = [1, 2, 3].filter(floor => floor.toString() !== currentFloor);

            otherFloors.forEach(floor => {
                pendingRequests++;
                $.ajax({
                    url: 'get_booked_tables.php',
                    type: 'GET',
                    data: { useDate: selectedDate, useZone: floor },
                    dataType: 'json',
                    success: (booked) => {
                        if (window.FloorOccupancy) {
                            window.FloorOccupancy.updateFloorOccupancy(floor, booked.length);
                        }
                        this.handleRequestComplete(--pendingRequests);
                    },
                    error: () => {
                        this.handleRequestComplete(--pendingRequests);
                    }
                });
            });

            if (pendingRequests === 0) {
                this.resetLoadingState();
            }
        },

        handleRequestComplete: function(remainingRequests) {
            if (remainingRequests === 0) {
                this.resetLoadingState();
            }
        },

        resetLoadingState: function() {
            window.updatingAllFloors = false;
            window.FloorUtils.hideLoadingOverlay();
        }
    };

    /**
     * Event Handlers and Initialization
     */
    window.EventHandlers = {
        init: function() {
            this.initializePage();
            this.bindDateChangeHandler();
            this.bindTabChangeHandler();
            this.initializeTooltips();
        },

        initializePage: function() {
            // Initialize utility functions
            window.UtilityFunctions.initializeDateFunctions();

            // Load all floors data initially
            window.FloorplanController.loadAllFloors();

            // Check for existing bookings
            const initialFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';
            const currentDate = $('#myDate').val();
            console.log('=== Initial Page Load ===');
            console.log('Initial floor:', initialFloor, 'Current date:', currentDate);

            this.checkExistingBookings(currentDate, initialFloor);
        },

        bindDateChangeHandler: function() {
            $('#myDate').off('change').on('change', (event) => {
                const selectedDate = $(event.target).val();
                console.log('Date changed to:', selectedDate);

                const apiDate = window.formatDateForAPI(selectedDate);
                window.currentApiDate = apiDate;

                const currentFloor = $('.nav-link.active').attr('href').replace('#floor', '') || '1';
                this.checkExistingBookings(selectedDate, currentFloor);
                window.FloorplanController.loadAllFloors();
            });
        },

        bindTabChangeHandler: function() {
            $('.nav-link').on('click', (event) => {
                window.FloorUtils.showLoadingOverlay();
                window.FloorUtils.resetAllTableStates();

                const floorId = $(event.target).attr('href').replace('#', '');
                const floorNumber = floorId.replace('floor', '');
                const selectedDate = $('#myDate').val();

                console.log('Tab changed to floor:', floorNumber, 'Selected date:', selectedDate);

                if (floorNumber >= 1 && floorNumber <= 3) {
                    const apiDate = window.formatDateForAPI(selectedDate);
                    window.currentZone = floorNumber;
                    $('#useZone').val(floorNumber);

                    this.checkExistingBookings(selectedDate, floorNumber);

                    if (window.DataLoader) {
                        window.DataLoader.loadBookedTables(apiDate, floorNumber);
                    }
                }
            });
        },

        checkExistingBookings: function(date, floor) {
            console.log('Checking bookings for date:', date, 'floor:', floor);
            // Button is always enabled - let backend handle validation
        },

        initializeTooltips: function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    };

    // Legacy function compatibility
    window.loadAllFloors = window.FloorplanController.loadAllFloors.bind(window.FloorplanController);
    window.checkExistingBookings = window.EventHandlers.checkExistingBookings.bind(window.EventHandlers);
    window.disableBuyoutButton = function(reason) { console.log('Button disable requested:', reason); };
    window.enableBuyoutButton = function() { console.log('Button enable requested'); };
    window.showLoadingOverlay = window.FloorUtils.showLoadingOverlay.bind(window.FloorUtils);
    window.hideLoadingOverlay = window.FloorUtils.hideLoadingOverlay.bind(window.FloorUtils);
    window.resetAllTableStates = window.FloorUtils.resetAllTableStates.bind(window.FloorUtils);
    window.getTotalTablesForFloor = window.FloorUtils.getTotalTablesForFloor.bind(window.FloorUtils);
    window.showFloorBuyoutIndicator = window.FloorUtils.showFloorBuyoutIndicator.bind(window.FloorUtils);
    window.hideFloorBuyoutIndicator = window.FloorUtils.hideFloorBuyoutIndicator.bind(window.FloorUtils);
    window.updateFloorOccupancy = window.FloorOccupancy.updateFloorOccupancy.bind(window.FloorOccupancy);
    window.loadBookedTables = window.DataLoader.loadBookedTables.bind(window.DataLoader);
    window.loadDisabledTables = window.DataLoader.loadDisabledTables.bind(window.DataLoader);
    window.markBookedTables = window.TableManager.markBookedTables.bind(window.TableManager);
    window.markDisabledTables = window.TableManager.markDisabledTables.bind(window.TableManager);

    // Initialize the application
    window.EventHandlers.init();

});
</script>

